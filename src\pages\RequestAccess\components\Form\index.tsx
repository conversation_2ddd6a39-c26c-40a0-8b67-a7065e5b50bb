import { Button, Heading, InputTextBox, Text, useToast } from '@onyma-ds/react';
import { Link, useNavigate } from 'react-router-dom';
import { useApi } from '@/contexts/api';
import { Spinner } from '@/components';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { requestAccessSchema, RequestAccessFormType } from './validateForm';
import * as SC from './styles';

export default function Form() {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const {
    auth: { requestAccess },
  } = useApi();

  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<RequestAccessFormType>({
    resolver: zodResolver(requestAccessSchema),
    defaultValues: {
      name: '',
      company: '',
      email: '',
      role: '',
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: requestAccess,
    onSuccess: () => {
      addToast({
        title: 'Solicitação enviada com sucesso',
        description: 'Sua solicitação foi enviada com sucesso',
        type: 'success',
      });
      navigate('/login', { replace: true });
    },
    onError: (error: Error) => {
      addToast({
        title: 'Erro ao solicitar acesso. Tente novamente mais tarde.',
        description: error.message,
        type: 'error',
      });
    },
  });

  const onSubmit = (data: RequestAccessFormType) => {
    mutate(data);
  };

  return (
    <SC.Container
      noValidate
      onSubmit={handleSubmit(onSubmit)}
    >
      <Heading
        as="h2"
        type="heading_02"
      >
        Solicitação de acesso <br /> <b>Portal BenCorp</b>
      </Heading>
      <Controller
        name="name"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="Nome completo"
            placeholder="Nome completo"
            error={!!errors.name}
            feedbackText={errors.name?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="company"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="Empresa"
            placeholder="Empresa"
            error={!!errors.company}
            feedbackText={errors.company?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="email"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="E-mail"
            type="email"
            placeholder="E-mail"
            error={!!errors.email}
            feedbackText={errors.email?.message}
            {...field}
          />
        )}
      />

      <Controller
        name="role"
        control={control}
        render={({ field }) => (
          <InputTextBox
            label="Cargo"
            placeholder="Cargo"
            error={!!errors.role}
            feedbackText={errors.role?.message}
            {...field}
          />
        )}
      />

      <Button
        type="submit"
        variant="secondary"
        color="white"
        disabled={isPending}
      >
        {isPending ? (
          <Spinner
            size={16}
            color="white"
          />
        ) : (
          'Enviar'
        )}
      </Button>
      <SC.RequestAccessBox>
        <Text>ou</Text>
        <Link to="/login">Entrar</Link>
      </SC.RequestAccessBox>
    </SC.Container>
  );
}
