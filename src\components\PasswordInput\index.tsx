import { forwardRef, useState } from 'react';
import { PasswordInputProps } from './types';
import * as SC from './styles';

const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  (props, ref) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
      <SC.Container>
        <SC.Input
          ref={ref}
          type={isVisible ? 'text' : 'password'}
          {...props}
        />
        <SC.Icon
          title={isVisible ? 'Ocultar senha' : 'Mostrar senha'}
          name={isVisible ? 'visibility_off' : 'visibility'}
          onClick={() => setIsVisible((curr) => !curr)}
        />
      </SC.Container>
    );
  },
);

export default PasswordInput;
