import styled from 'styled-components';
import { Button, Checkbox, Heading, Text } from '@onyma-ds/react';
import { Icons } from '@/components';

export const LoginHeader = styled.header``;

export const LoginBody = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xs};
`;

export const LoginFooter = styled.footer`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xs};
`;

export const LoginForm = styled.form`
  color: ${({ theme }) => theme.colors.gray_40};
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.lg};

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    max-width: 320px;
  }
`;

export const LoginHeading = styled(Heading).attrs({
  type: 'heading_03',
})`
  font-weight: 600;
  color: ${({ theme }) => theme.colors.black};
`;

export const Field = styled.div`
  display: grid;
  grid-template-rows: repeat(2, auto) 17px;
  gap: ${({ theme }) => theme.spacings.quark};
`;

export const Label = styled.label`
  color: ${({ theme }) => theme.colors.black};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const ErrorMessage = styled.span`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const CheckboxLabel = styled(Checkbox.Label)`
  color: ${({ theme }) => theme.colors.black};
  font-size: ${({ theme }) => theme.fontSizes.md};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const LoginButton = styled(Button).attrs({
  variant: 'secondary',
  color: 'white',
})`
  width: 100%;
  font-weight: 600;
  display: grid;
  place-items: center;
`;

export const ResetPasswordButton = styled(Button).attrs({
  variant: 'secondary',
  buttonType: 'tertiary',
})`
  width: 100%;
  font-weight: 600;

  display: flex;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacings.nano};

  &:hover {
    background-color: color-mix(
      in srgb,
      ${({ theme }) => theme.colors.secondary} 12%,
      transparent
    );
  }
`;

export const RoundedIcon = styled(Icons.Symbol).attrs({
  type: 'rounded',
  size: 20,
})``;

export const RequestAccessText = styled(Text).attrs({
  type: 'body_03',
})`
  text-align: center;
  padding: ${({ theme }) => `${theme.spacings.xxxs} ${theme.spacings.xs}`};
  color: ${({ theme }) => theme.colors.gray_40};

  a {
    text-decoration: underline;
  }
`;
