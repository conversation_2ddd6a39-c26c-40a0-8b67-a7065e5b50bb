import styled from 'styled-components';

export const Container = styled.div`
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    grid-template-columns: auto 1fr;
  }
`;

export const Content = styled.section`
  max-height: 100vh;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
`;

export const RouteWrapper = styled.div`
  width: 100%;
  height: 100%;
  overflow-y: auto;
`;
