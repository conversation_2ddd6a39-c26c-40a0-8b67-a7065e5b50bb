import { Icons } from '@/components';
import { useApi } from '@/contexts/api';
import { useToast } from '@onyma-ds/react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { PageHeader } from '../../components';
import { BIContainer } from './components';
import { AuthStorageService } from '@/services/storage/AuthStorageService';
import * as SC from './styles';
import { KEY_USER } from '@/services/storage/keys';

export default function BiIdPage() {
  const params = useParams();
  const { addToast } = useToast();
  const { menus } = useApi();

  const [biData, setBiData] = useState<{
    name: string;
    powerBiId: string;
    powerBiNome: string;
    uuidEmpresa: string | null;
  }>({
    name: '',
    powerBiId: '',
    powerBiNome: '',
    uuidEmpresa: null,
  });

  useEffect(() => {
    const storedData = localStorage.getItem(KEY_USER);
    const parsedData = storedData ? JSON.parse(storedData) : null;
    const companyId = parsedData?.companyId || null;

    if (params.biId) {
      menus
        .loadMenu({ id: params.biId })
        .then((result) => {
          const uuidEmpresa = companyId;
          setBiData({
            powerBiId: result.result.bi?.powerBiId || '',
            powerBiNome: result.result.biPagina?.powerBiNome || '',
            name: result.result.name,
            uuidEmpresa: uuidEmpresa,
          });
        })
        .catch((error) => {
          if (
            error.response?.status === 403 &&
            error.response?.data?.code === 'TokenExpired'
          ) {
            addToast({
              type: 'error',
              title: 'Sua sessão do portal expirou',
              description:
                'Sua sessão expirou, faça login novamente para acessar esse relatório.',
              timeout: 5000,
            });
            AuthStorageService.clear();
            window.location.replace('/login');
          } else {
            addToast({
              type: 'error',
              title: error.response?.data?.title || 'Ocorreu um erro!',
              description:
                error.response?.data?.message ||
                'Um erro não especificado ocorreu, por favor entre em contato com o suporte.',
              timeout: 5000,
            });
          }
        });
    }
  }, [params, menus, addToast]);

  return (
    <SC.Container>
      <SC.Section>
        <PageHeader.Root>
          <PageHeader.TitleBox>
            <Icons.RAFa6.FaFolderOpen size={24} />
            <PageHeader.Title>{biData.name}</PageHeader.Title>
          </PageHeader.TitleBox>
          <PageHeader.BackButton />
        </PageHeader.Root>
        <BIContainer
          powerBiId={biData.powerBiId}
          powerBiNome={biData.powerBiNome}
          idEmpresaCliente={biData.uuidEmpresa}
        />
      </SC.Section>
    </SC.Container>
  );
}
