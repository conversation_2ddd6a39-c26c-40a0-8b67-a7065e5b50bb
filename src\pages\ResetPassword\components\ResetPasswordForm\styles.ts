import styled from 'styled-components';
import { Button, Heading } from '@onyma-ds/react';
import { Icons } from '@/components';

export const ResetPasswordForm = styled.form`
  width: 100%;
  max-width: 23rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.lg};
`;

export const ResetPasswordHeader = styled.header``;

export const ResetPasswordHeading = styled(Heading).attrs({
  type: 'heading_03',
})`
  font-weight: 600;
`;

export const ResetPasswordBody = styled.div``;

export const Field = styled.div`
  display: grid;
  grid-template-rows: repeat(2, auto) 17px;
  gap: ${({ theme }) => theme.spacings.quark};
`;

export const Label = styled.label`
  color: ${({ theme }) => theme.colors.black};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const ErrorMessage = styled.span`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const ResetPasswordFooter = styled.footer`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xs};
`;

export const ResetPasswordButton = styled(Button).attrs({
  variant: 'secondary',
  color: 'white',
})`
  font-weight: 600;
  display: grid;
  place-items: center;
`;

export const BackToLoginButton = styled(Button).attrs({
  variant: 'secondary',
  buttonType: 'tertiary',
})`
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacings.nano};

  &:hover {
    background-color: color-mix(
      in srgb,
      ${({ theme }) => theme.colors.secondary} 12%,
      transparent
    );
  }

  ${ResetPasswordFooter} & {
    width: 100%;
  }
`;

export const RoundedIcon = styled(Icons.Symbol).attrs({
  type: 'rounded',
  size: 20,
})``;
