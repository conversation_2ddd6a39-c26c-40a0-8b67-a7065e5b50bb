import { Link } from 'react-router-dom';
import { LinkSentMessageProps } from './types';
import * as SC from './styles';

export default function LinkSentMessage({ email }: LinkSentMessageProps) {
  return (
    <SC.LinkSentContainer>
      <SC.LinkSentHeading>
        Se existir uma conta com o e-mail{' '}
        <SC.EmailHighlight>{email}</SC.EmailHighlight>, você receberá um link
        para a redefinição da senha.
      </SC.LinkSentHeading>
      <Link to="/login">
        <SC.AccessMyAccountButton type="button">
          Acessar minha conta
        </SC.AccessMyAccountButton>
      </Link>
    </SC.LinkSentContainer>
  );
}
