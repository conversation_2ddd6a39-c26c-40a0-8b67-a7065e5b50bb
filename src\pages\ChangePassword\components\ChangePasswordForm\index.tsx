import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useApi } from '@/contexts/api';
import { PasswordInput, Spinner } from '@/components';
import {
  changePasswordFormSchema,
  ChangePasswordFormValues,
} from './validation';
import { ChangePasswordFormProps } from './types';
import * as SC from './styles';

export default function ChangePasswordForm({
  token,
  onSuccess,
  onError,
}: ChangePasswordFormProps) {
  const { auth } = useApi();
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordFormSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onSubmit: SubmitHandler<ChangePasswordFormValues> = async ({
    newPassword,
  }) => {
    try {
      await auth.changePasswordWithToken({ token, senha: newPassword });
      onSuccess();
    } catch (error) {
      onError();
    }
  };

  return (
    <SC.Form onSubmit={handleSubmit(onSubmit)}>
      <SC.Header>
        <SC.FormHeading>Defina uma nova senha</SC.FormHeading>
      </SC.Header>
      <SC.Body>
        <SC.Field>
          <SC.Label htmlFor="new-password">Nova senha</SC.Label>
          <PasswordInput
            id="new-password"
            placeholder="Digite a nova senha"
            {...register('newPassword')}
          />
          {errors.newPassword && (
            <SC.ErrorMessage role="alert">
              {errors.newPassword.message}
            </SC.ErrorMessage>
          )}
        </SC.Field>
        <SC.Field>
          <SC.Label htmlFor="confirm-password">
            Confirmação da nova senha
          </SC.Label>
          <PasswordInput
            id="confirm-password"
            placeholder="Digite a nova senha"
            {...register('confirmPassword')}
          />
          {errors.confirmPassword && (
            <SC.ErrorMessage role="alert">
              {errors.confirmPassword.message}
            </SC.ErrorMessage>
          )}
        </SC.Field>
      </SC.Body>
      <SC.Footer>
        <SC.ChangePasswordButton
          type="submit"
          disabled={
            !!errors.newPassword || !!errors.confirmPassword || isSubmitting
          }
        >
          {isSubmitting ? (
            <Spinner
              size={20}
              color="white"
            />
          ) : (
            'Alterar senha'
          )}
        </SC.ChangePasswordButton>
      </SC.Footer>
    </SC.Form>
  );
}
