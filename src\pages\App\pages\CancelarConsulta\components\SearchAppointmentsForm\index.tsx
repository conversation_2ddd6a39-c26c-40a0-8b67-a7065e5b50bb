/* eslint-disable react-hooks/exhaustive-deps */
import { useSearchParams } from 'react-router-dom';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { InputDateBox, InputTextBox, SelectBox } from '@onyma-ds/react';
import { SubmitButton } from '..';
import { schema } from './utils/validation';
import { FormFields, SearchAppointmentsFormProps } from './types';
import * as SC from './styles';
import { useAuth } from '@/contexts/auth';
import { useQuery } from '@tanstack/react-query';
import { useApi } from '@/contexts/api';
import { useEffect } from 'react';

export default function SearchAppointmentsForm({
  defaultValues,
  calendarOptions,
}: SearchAppointmentsFormProps) {
  const { user } = useAuth();
  const {
    clients: { loadClientCompanies },
  } = useApi();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    setValue,
    watch,
    control,
  } = useForm<FormFields>({
    mode: 'all',
    reValidateMode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues,
  });

  const { data } = useQuery({
    queryKey: ['companies'],
    queryFn: () => loadClientCompanies(),
    refetchOnWindowFocus: false,
  });

  const isCustomer =
    user.currentRole.name === 'Gestor Cliente' ||
    user.currentRole.name === 'Colaborador Cliente' ||
    user.currentRole.name === 'Colaborador (Cliente)' ||
    user.currentRole.name === 'RH Cliente';

  const onSubmit = (data: FormFields) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('dataInicial', data.startDate.toISOString());
    newSearchParams.set('dataFinal', data.endDate.toISOString());
    newSearchParams.set('codigoUsuarioAgenda', data.calendar);
    newSearchParams.set('empresa', data.companyName.value);
    newSearchParams.set('matricula', data.registration);
    setSearchParams(newSearchParams);
  };

  useEffect(() => {
    if (isCustomer) {
      const findCompany = data?.result.find(
        (company) => company.id === user.companyId,
      );
      setValue('companyName', {
        label: findCompany?.name ?? '',
        value: findCompany?.codigoSoc ?? '',
      });
      return;
    }
    setValue('companyName', {
      label: data?.result[0]?.name ?? '',
      value: data?.result[0]?.codigoSoc ?? '',
    });
  }, [data]);

  return (
    <SC.SearchAppointmentsForm onSubmit={handleSubmit(onSubmit)}>
      <SelectBox
        label="Agenda"
        placeholder="Selecione uma agenda"
        disabled={isSubmitting}
        error={!!errors.calendar}
        feedbackText={errors.calendar?.message}
        options={calendarOptions}
        onSelect={(option) => setValue('calendar', option.value)}
        optionSelected={calendarOptions.find(
          (calendar) => calendar.value === watch('calendar'),
        )}
      />
      <SC.Grid $autoFlow="column">
        <InputDateBox
          label="Data inicial"
          disabled={isSubmitting}
          error={!!errors.startDate}
          feedbackText={errors.startDate?.message}
          onSelectDate={(date) => setValue('startDate', date as Date)}
          selectedDate={watch('startDate')}
        />
        <InputDateBox
          label="Data final"
          disabled={isSubmitting}
          error={!!errors.endDate}
          feedbackText={errors.endDate?.message}
          onSelectDate={(date) => setValue('endDate', date as Date)}
          selectedDate={watch('endDate')}
        />
      </SC.Grid>
      {!isCustomer && (
        <>
          <Controller
            name="companyName"
            control={control}
            render={({ field: { onChange, value } }) => (
              <SelectBox
                label="Empresa"
                placeholder="Selecione uma empresa"
                options={data?.result
                  .map((company) => ({
                    label: company.name,
                    value: company.codigoSoc as string,
                  }))
                  .filter((e) => e.value)
                  .sort((a, b) => a.label.localeCompare(b.label))}
                optionSelected={value}
                disabled={isSubmitting}
                error={!!errors.companyName}
                feedbackText={errors.companyName?.message}
                onSelect={(option) => onChange(option)}
              />
            )}
          />
          <InputTextBox
            label="Matrícula"
            defaultValue={defaultValues?.registration}
            placeholder="Digite a matrícula"
            disabled={isSubmitting}
            error={!!errors.registration}
            feedbackText={errors.registration?.message}
            onChangeValue={(value) => setValue('registration', value)}
          />
        </>
      )}
      <SubmitButton
        variant="secondary"
        color="white"
        isSubmitting={isSubmitting}
      >
        {isSubmitting ? 'Buscando consultas...' : 'Buscar consultas'}
      </SubmitButton>
    </SC.SearchAppointmentsForm>
  );
}
