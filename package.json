{"name": "bendash", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "vite --mode development", "start:prod": "vite --mode production", "build:dev": "tsc && vite build --mode development", "build:prod": "tsc && vite build --mode production", "preview:dev": "yarn build:dev && vite preview", "preview:prod": "yarn build:prod && vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "yarn lint --fix", "format": "prettier --write .", "test": "jest --passWithNoTests --no-cache --maxWorkers=25%", "test:staged": "yarn test -- --only<PERSON><PERSON>ed", "test:watch": "yarn test -- --watchAll", "test:ci": "yarn test -- --coverage", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@onyma-ds/react": "^1.14.9", "@onyma-ds/tokens": "^1.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collection": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-query": "^5.32.1", "@tanstack/react-query-devtools": "^5.32.1", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.0.8", "lucide-react": "^0.486.0", "powerbi-client": "^2.22.4", "powerbi-client-react": "^1.4.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-player": "^2.16.0", "react-router-dom": "^6.22.0", "remark": "^15.0.1", "remark-parse": "^11.0.0", "styled-components": "^6.1.8", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.0.14", "tw-animate-css": "^1.2.5", "zod": "^3.23.5"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@swc/jest": "^0.2.36", "@tanstack/eslint-plugin-query": "^5.32.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.12", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "git-commit-msg-linter": "^5.0.6", "husky": "^9.0.11", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-offline": "^1.0.1", "jest-styled-components": "^7.2.0", "jest-transformer-svg": "^2.0.2", "lint-staged": "^15.2.2", "postcss": "^8.5.3", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^5.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}