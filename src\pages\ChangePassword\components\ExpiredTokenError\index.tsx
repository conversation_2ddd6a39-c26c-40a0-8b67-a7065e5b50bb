import { Link } from 'react-router-dom';
import * as SC from './styles';

export default function ExpiredTokenError() {
  return (
    <SC.Main>
      <SC.Header>
        <SC.RoundedIcon name="error" />
        <SC.Title>
          O link para recuperação de senha expirou. Por favor, solicite um novo
          link para continuar.
        </SC.Title>
      </SC.Header>
      <Link to="/esqueci-minha-senha">
        <SC.RequestNewTokenButton type="button">
          Solicitar novo link
        </SC.RequestNewTokenButton>
      </Link>
    </SC.Main>
  );
}
