import { Submit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/contexts/auth';
import { useApi } from '@/contexts/api';
import { PasswordInput, Spinner } from '@/components';
import {
  changeDefaultPasswordFormSchema,
  ChangeDefaultPasswordFormValues,
} from './validation';
import { ChangeDefaultPasswordFormProps } from './types';
import * as SC from './styles';

export default function ChangeDefaultPasswordForm({
  onSuccess,
}: ChangeDefaultPasswordFormProps) {
  const { user } = useAuth();
  const { auth } = useApi();
  const {
    formState: { errors, isSubmitting },
    register,
    setError,
    clearErrors,
    handleSubmit,
  } = useForm<ChangeDefaultPasswordFormValues>({
    resolver: zodResolver(changeDefaultPasswordFormSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit: SubmitHandler<ChangeDefaultPasswordFormValues> = async (
    data,
  ) => {
    try {
      clearErrors('root');
      await auth.changePassword({
        userId: user.uuid,
        password: data.password,
        passwordRepeat: data.confirmPassword,
        checkCurrent: false,
      });
      onSuccess();
    } catch (error) {
      setError('root', { type: 'custom', message: error.message });
    }
  };

  return (
    <SC.Form onSubmit={handleSubmit(onSubmit)}>
      <SC.FormHeader>
        <SC.FormHeading>Defina uma nova senha</SC.FormHeading>
      </SC.FormHeader>
      <SC.FormBody>
        <SC.Field>
          <SC.Label htmlFor="password">Nova senha</SC.Label>
          <PasswordInput
            id="password"
            placeholder="Digite a nova senha"
            {...register('password')}
          />
          {errors.password && (
            <SC.ErrorMessage role="alert">
              {errors.password.message}
            </SC.ErrorMessage>
          )}
        </SC.Field>
        <SC.Field>
          <SC.Label htmlFor="confirm-password">
            Confirmação da nova senha
          </SC.Label>
          <PasswordInput
            id="confirm-password"
            placeholder="Digite a nova senha"
            {...register('confirmPassword')}
          />
          {errors.confirmPassword && (
            <SC.ErrorMessage role="alert">
              {errors.confirmPassword.message}
            </SC.ErrorMessage>
          )}
        </SC.Field>
        {errors.root && (
          <SC.ErrorMessage role="alert">{errors.root.message}</SC.ErrorMessage>
        )}
      </SC.FormBody>
      <SC.FormFooter>
        <SC.ChangePasswordButton
          type="submit"
          disabled={
            !!errors.password || !!errors.confirmPassword || isSubmitting
          }
        >
          {isSubmitting ? (
            <Spinner
              size={20}
              color="white"
            />
          ) : (
            'Alterar senha'
          )}
        </SC.ChangePasswordButton>
      </SC.FormFooter>
    </SC.Form>
  );
}
