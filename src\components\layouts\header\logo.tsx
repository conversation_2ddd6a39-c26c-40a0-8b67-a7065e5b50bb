import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { Link } from 'react-router-dom';

export default function LogoLink() {
  const { company } = useCompany();
  const { user } = useAuth();

  const hasLogo = company?.logo && user.currentRole.requiresCompany;
  const companyLogo = company?.logo as string;

  return (
    <div className="relative z-10 max-w-[180px] m-auto">
      <Link to="/app/home">
        <img
          className="w-full max-h-[100px] py-4 px-0"
          alt="logo da empresa"
          src={
            hasLogo
              ? companyLogo[0] === '/'
                ? `${import.meta.env.VITE_API_URL}/${company?.logo}`
                : company.logo ?? ''
              : '/imgs/bencorp-logo.png'
          }
        />
      </Link>
    </div>
  );
}
