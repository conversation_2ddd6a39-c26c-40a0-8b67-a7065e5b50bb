import { Header } from '@/components/layouts/header';
import { useAuth } from '@/contexts/auth';
import { Navigate, useMatch } from 'react-router-dom';
import { Outlet } from 'react-router-dom';
import * as SC from './styles';
import { MenuSideBar } from '@/pages/App/components';

export default function PrivateLayout() {
  const { user } = useAuth();

  const matchAppRoute = useMatch({ path: '/app', end: true });

  if (!user) {
    return (
      <Navigate
        to="/logout"
        replace
      />
    );
  }

  if (matchAppRoute) {
    return (
      <Navigate
        to="/app/home"
        replace
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col flex-1">
      <Header />
      <div className="h-full"></div>
      {/* <MenuSideBar />
      <SC.Content>
        <SC.RouteWrapper>
          <Outlet />
        </SC.RouteWrapper>
      </SC.Content> */}
    </div>
  );
}
