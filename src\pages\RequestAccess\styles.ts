import styled from 'styled-components';

export const Container = styled.main`
  width: 100%;
  padding: 7rem 1rem;
`;

export const Content = styled.section`
  max-width: 510px;
  margin: 0 auto;
  display: flex;
  flex-direction: column-reverse;
  gap: 2rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    max-width: 1140px;
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    justify-content: center;
  }
`;

export const ImageBox = styled.div`
  max-width: 100%;

  img {
    max-width: 100%;
    height: auto;
  }
`;
