import { Spinner } from '@/components/loaders/spinner';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useQuery } from '@tanstack/react-query';
import { Card } from '../../components';
import { moduleOptions } from './data';
import * as SC from './styles';

export default function HomePage() {
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const {
    data: allMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['menus', user],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: user.companyId as string,
        perfil: user?.currentRole.id,
      }),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <SC.Container>
      <SC.Cards>
        {allMenus?.result?.map((menu) => {
          const moduleExist = moduleOptions.find(
            (moduleOption) =>
              moduleOption.value.toLowerCase() === menu.module.toLowerCase(),
          );
          if (moduleExist) {
            return (
              <SC.CardItem key={moduleExist.name}>
                <Card
                  imgSrc={moduleExist.image || '/imgs/indicador_01.jpg'}
                  imgAlt="Figura representando uma pessoa apontando para um calendário"
                  title={moduleExist.name}
                  linkTo={moduleExist.url}
                />
              </SC.CardItem>
            );
          }
        })}
      </SC.Cards>
    </SC.Container>
  );
}
