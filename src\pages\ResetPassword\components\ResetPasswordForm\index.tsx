import { Link } from 'react-router-dom';
import { InputText, useToast } from '@onyma-ds/react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useApi } from '@/contexts/api';
import { Spinner } from '@/components';
import { ResetPasswordFormData, resetPasswordFormSchema } from './validation';
import { ResetPasswordFormProps } from './types';
import * as SC from './styles';

export default function ResetPasswordForm({
  onLinkSent,
}: ResetPasswordFormProps) {
  const { auth } = useApi();
  const { addToast } = useToast();
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordFormSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit: SubmitHandler<ResetPasswordFormData> = async ({ email }) => {
    try {
      await auth.sendResetPasswordLink({ email });
      onLinkSent(email);
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Erro ao tentar recuperar senha',
        description: error.message,
        timeout: 5000,
      });
    }
  };

  return (
    <SC.ResetPasswordForm onSubmit={handleSubmit(onSubmit)}>
      <SC.ResetPasswordHeader>
        <SC.ResetPasswordHeading>Recuperar senha</SC.ResetPasswordHeading>
      </SC.ResetPasswordHeader>
      <SC.ResetPasswordBody>
        <SC.Field>
          <SC.Label htmlFor="email">E-mail</SC.Label>
          <InputText
            id="email"
            type="email"
            placeholder="Digite o seu e-mail"
            {...register('email')}
          />
          {errors.email && (
            <SC.ErrorMessage role="alert">
              {errors.email.message}
            </SC.ErrorMessage>
          )}
        </SC.Field>
      </SC.ResetPasswordBody>
      <SC.ResetPasswordFooter>
        <SC.ResetPasswordButton
          type="submit"
          disabled={!!errors.email || isSubmitting}
        >
          {isSubmitting ? (
            <Spinner
              color="white"
              size={20}
            />
          ) : (
            'Recuperar senha'
          )}
        </SC.ResetPasswordButton>
        <Link to="/login">
          <SC.BackToLoginButton type="button">
            <SC.RoundedIcon name="chevron_left" />
            Voltar ao login
          </SC.BackToLoginButton>
        </Link>
      </SC.ResetPasswordFooter>
    </SC.ResetPasswordForm>
  );
}
