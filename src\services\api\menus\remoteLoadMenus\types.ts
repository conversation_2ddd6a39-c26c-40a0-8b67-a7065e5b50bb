import { ApiResult } from '@/@types/ApiResult';

type Params = {
  companyId?: string;
  url?: string;
  ativo?: boolean;
  modulo?: string;
  empresa?: string;
  perfil?: string;
  menuTipo?: string;
};

type Result = ApiResult<SystemMenu[]>;

export type Menu = {
  id: string;
  title: string;
  description: string | null;
  url: string;
  position: string;
  type: number | null;
  image?: null | string;
  uuid?: string;
  submenus?: Menu[];
  menus: Menu[];
};

export type SystemMenu = {
  module: string;
  menus: Menu[];
};

export type RemoteLoadMenus = (params?: Params) => Promise<Result>;
