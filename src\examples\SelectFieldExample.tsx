import React, { useState } from 'react';
import { SelectField } from '@/components/ui/select';
import { LabelAndValue } from '@/@types/LabelAndValue';

// Example usage of the updated SelectField component
export function SelectFieldExample() {
  const [selectedValue, setSelectedValue] = useState<LabelAndValue | undefined>(
    undefined,
  );

  // Sample options with label and value
  const options: LabelAndValue[] = [
    { label: 'Opção 1', value: '1' },
    { label: 'Opção 2', value: '2' },
    { label: 'Opção 3', value: '3' },
    { label: 'Opção 4', value: '4' },
  ];

  const handleValueChange = (option: LabelAndValue) => {
    setSelectedValue(option);
    console.log('Selected option:', option);
  };

  return (
    <div className="p-6 max-w-md">
      <h2 className="text-xl font-semibold mb-4">SelectField Example</h2>

      {/* Basic usage with controlled value */}
      <SelectField
        label="Selecione uma opção"
        placeholder="Escolha uma opção..."
        options={options}
        value={selectedValue}
        onValueChange={handleValueChange}
        className="mb-4"
      />

      {/* Usage with default value */}
      <SelectField
        label="Com valor padrão"
        placeholder="Escolha uma opção..."
        options={options}
        defaultValue={{ label: 'Opção 2', value: '2' }}
        onValueChange={(option) => {
          console.log('Default example - Option:', option);
        }}
        className="mb-4"
      />

      {/* Usage with error message */}
      <SelectField
        label="Com mensagem de erro"
        placeholder="Escolha uma opção..."
        options={options}
        errorMessage="Este campo é obrigatório"
        className="mb-4"
      />

      {/* Disabled state */}
      <SelectField
        label="Desabilitado"
        placeholder="Não é possível selecionar"
        options={options}
        disabled
        className="mb-4"
      />

      {/* Small size variant */}
      <SelectField
        label="Tamanho pequeno"
        placeholder="Escolha uma opção..."
        options={options}
        size="sm"
      />

      <div className="mt-6 p-4 bg-gray-100 rounded">
        <p className="text-sm">
          <strong>Valor selecionado:</strong> {selectedValue?.label || 'Nenhum'}
        </p>
        <p className="text-sm">
          <strong>Value:</strong> {selectedValue?.value || 'Nenhum'}
        </p>
      </div>
    </div>
  );
}
