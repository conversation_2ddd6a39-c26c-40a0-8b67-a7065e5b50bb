import { z } from 'zod';

const required_error = 'Campo obrigatório';

export const schema = z
  .object({
    calendar: z
      .string({ message: 'Agenda é obrigatória' })
      .min(1, 'Agenda é obrigatória'),
    startDate: z.date({ message: 'Data inicial é obrigatória' }),
    endDate: z.date({ message: 'Data final é obrigatória' }),
    companyName: z.object(
      {
        label: z.string({ required_error }),
        value: z.string({ required_error }),
      },
      { required_error },
    ),
    registration: z
      .string({ message: required_error })
      .min(1, 'Matrícular é obrigatória'),
  })
  .superRefine((arg, ctx) => {
    if (arg.endDate < arg.startDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['endDate'],
        message: 'Data final deve ser posterior à data inicial',
      });
    }
  });
