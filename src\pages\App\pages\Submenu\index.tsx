import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { Card } from '@/pages/App/components';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { ContainerLoading } from '../Indicators/styles';
import { handleCardClick } from '../Apps/utils';
import * as SC from './styles';

export default function SubmenuPage() {
  const [searchParams] = useSearchParams();
  const submenuId = searchParams.get('id');

  const {
    menus: { loadMenu },
  } = useApi();

  const {
    data: allSubMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['submenu', submenuId],
    queryFn: () =>
      loadMenu({
        id: String(submenuId),
      }),
    refetchOnWindowFocus: false,
  });

  if (isLoading || isFetching) {
    return (
      <ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </ContainerLoading>
    );
  }

  return (
    <SC.Container>
      <SC.Cards>
        {allSubMenus?.result.menus ? (
          allSubMenus?.result.menus[0].menus?.map((menu) => (
            <SC.CardItem key={menu.id}>
              <Card
                key={menu.id}
                imgSrc={menu.image || '/imgs/indicador_01.jpg'}
                imgAlt={menu.description ?? ''}
                description={menu.description ?? ''}
                title={menu.title}
                linkTo={handleCardClick(menu)}
                targetBlank={Number(menu.type) === 4}
              />
            </SC.CardItem>
          ))
        ) : (
          <SC.EmptySubmenu>
            <h2>Nenhum submenu cadastrado ainda.</h2>
          </SC.EmptySubmenu>
        )}
      </SC.Cards>
    </SC.Container>
  );
}
