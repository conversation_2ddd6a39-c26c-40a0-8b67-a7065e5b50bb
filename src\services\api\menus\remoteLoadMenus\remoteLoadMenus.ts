/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { Menu, RemoteLoadMenus } from '.';

const mapMenus = (menusData: any[]): Menu[] =>
  menusData
    .map((menu) => ({
      id: menu.uuid,
      title: menu.title || '',
      url: menu.url || '',
      type: menu.type || '',
      position: String(menu.position || ''),
      image: menu.image || '',
      menus: mapMenus(menu.submenus),
      description: menu.description,
      submenus: menu.submenus,
    }))
    .sort((a, b) => {
      if (a.position === null || a.position === undefined) return -1;
      if (b.position === null || a.position === undefined) return 1;
      if (Number.parseInt(a.position) < Number.parseInt(b.position)) return -1;
      if (Number.parseInt(a.position) > Number.parseInt(b.position)) return 1;
      return 0;
    });

export const remoteLoadMenus: RemoteLoadMenus = async (params = {}) => {
  const response = await httpClient.get('/utilidades/menu/fetchall', {
    params: {
      companyId: params.companyId,
      url: params.url,
      ativo: params.ativo,
      modulo: params.modulo,
      empresa: params.empresa,
      perfil: params.perfil,
      menuTipo: params.menuTipo,
    },
  });

  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: response.data.result?.map((item: any) => ({
      module: item.modulo,
      menus: mapMenus(item.menus),
    })),
  };
};
