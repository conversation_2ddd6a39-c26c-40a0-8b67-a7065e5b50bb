import styled from 'styled-components';

export const Container = styled.form`
  color: ${({ theme }) => theme.colors.gray_40};
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  label {
    color: inherit;
  }

  & > h2 {
    color: ${({ theme }) => theme.colors.black};
    font-size: 1.75rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  & > button {
    margin-top: 1.25rem;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    max-width: 320px;
  }

  button {
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;

export const RequestAccessBox = styled.section`
  display: flex;
  align-items: center;
  gap: 6px;

  p,
  a {
    font-size: 1rem;
    line-height: 100%;
  }

  a {
    text-decoration: underline;
  }
`;
