import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ptBR } from 'date-fns/locale';

import * as React from 'react';
import { Input } from './input';

interface DatePickerProps {
  date?: Date;
  onDateChange?: (date?: Date) => void;
  disabled?: boolean;
  placeholder?: string;
  availableDates?: Date[];
  label?: string;
  errorMessage?: string;
}

export function DatePicker({
  date,
  onDateChange,
  availableDates = [],
  label,
  errorMessage,
  placeholder,
  disabled,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (selectedDate: Date | undefined) => {
    onDateChange?.(selectedDate);
    setOpen(false);
  };

  const availableDatesMap = React.useMemo(() => {
    const map = new Map();

    availableDates.forEach((date) => {
      const key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
      map.set(key, true);
    });

    return map;
  }, [availableDates]);

  const disabledDays = React.useCallback(
    (day: Date) => {
      // Se não houver datas disponíveis, não desabilita nenhuma
      if (availableDatesMap.size === 0) return false;

      // Verificação rápida usando o Map
      const key = `${day.getFullYear()}-${day.getMonth()}-${day.getDate()}`;
      return !availableDatesMap.has(key);
    },
    [availableDatesMap],
  );

  const modifiersStyles = {
    available: { backgroundColor: '#d1fae5' }, // Verde claro para dias disponíveis
  };

  const modifiers = React.useMemo(() => {
    return {
      available: (date: Date) => {
        const key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
        return availableDatesMap.has(key);
      },
    };
  }, [availableDatesMap]);

  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
    >
      <PopoverTrigger asChild>
        <div className="flex flex-col gap-2">
          {label && (
            <label className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {label}
            </label>
          )}
          <Input
            type="text"
            value={date ? date.toLocaleDateString() : 'Selecione uma data'}
            disabled={disabled}
            placeholder={placeholder}
            errorMessage={errorMessage}
            className="cursor-pointer"
            endIcon="calendar"
          />
        </div>
      </PopoverTrigger>
      {!disabled && (
        <PopoverContent>
          <Calendar
            locale={ptBR}
            classNames={{
              day_selected: 'text-red-400',
              day: 'rounded-full w-7 h-7 text-black mr-1',
              button: 'bg-gray-100',
            }}
            mode="single"
            selected={date}
            onSelect={handleSelect}
            disabled={disabledDays}
            modifiers={modifiers}
            modifiersStyles={modifiersStyles}
            initialFocus
          />
        </PopoverContent>
      )}
    </Popover>
  );
}
